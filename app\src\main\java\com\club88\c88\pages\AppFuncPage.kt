package com.club88.c88.pages

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.paint
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.club88.c88.R
import kotlinx.coroutines.launch

@Composable
fun AppFuncPage(
    onCompleted: () -> Unit
) {
    val scope = rememberCoroutineScope()
    val pagerState = rememberPagerState(initialPage = 0, pageCount = {4})

    Box(modifier = Modifier.fillMaxSize()) {
        HorizontalPager(
            state = pagerState,
            modifier = Modifier.fillMaxSize()
        ) { page ->
            AppFuncItem(page = page, isLast = (page == 3)) {
                // 按钮点击时逻辑，比如结束引导或者做其他操作
                // 这里暂不写导航
                // TODO: navigate to home
                onCompleted()
            }
        }

        Row(
            modifier = Modifier
                .fillMaxWidth()
                .align(Alignment.Center),
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            IconButton(
                onClick = {
                    scope.launch {
                        if (pagerState.currentPage > 0) {
                            pagerState.animateScrollToPage(pagerState.currentPage - 1)
                        }
                    }
                },
                enabled = pagerState.currentPage > 0
            ) {
                if (pagerState.currentPage > 0) {
                    Icon(
                        painter = painterResource(id = R.drawable.arrow_left_blue),
                        modifier = Modifier.width(10.dp),
                        contentDescription = "Previous"
                    )
                }
            }

            IconButton(
                onClick = {
                    scope.launch {
                        if (pagerState.currentPage < 3) {
                            pagerState.animateScrollToPage(pagerState.currentPage + 1)
                        }
                    }
                },
                enabled = pagerState.currentPage < 3
            ) {
                if (pagerState.currentPage < 3) {
                    Icon(
                        painter = painterResource(id = R.drawable.arrow_right_blue),
                        modifier = Modifier.width(10.dp),
                        contentDescription = "Next"
                    )
                }
            }
        }
    }
}

@Composable
fun AppFuncItem(page: Int, isLast: Boolean, onStart: () -> Unit) {
    Box(
        modifier = Modifier
            .fillMaxSize()
            .paint(
                painterResource(id = R.drawable.splash_bg),
                contentScale = ContentScale.Crop
            ),
        contentAlignment = Alignment.Center
    ) {
        Column(horizontalAlignment = Alignment.CenterHorizontally) {
            Image(
                painter = painterResource(id = R.drawable.splash_p3),
                contentDescription = null,
                modifier = Modifier.width(140.dp)
            )
            Spacer(modifier = Modifier.height(50.dp))
            Image(
                painter = painterResource(
                    id = when (page) {
                        0 -> R.drawable.sp_img_0
                        1 -> R.drawable.sp_img_1
                        2 -> R.drawable.sp_img_2
                        else -> R.drawable.sp_img_3
                    }
                ),
                contentDescription = null
            )
            Spacer(modifier = Modifier.height(30.dp))
            Image(
                painter = painterResource(
                    id = when (page) {
                        0 -> R.drawable.sp_text_0
                        1 -> R.drawable.sp_text_1
                        2 -> R.drawable.sp_text_2
                        else -> R.drawable.sp_text_3
                    }
                ),
                contentDescription = null,
                modifier = Modifier.padding(horizontal = 16.dp)
            )

            if (isLast) {
                Spacer(modifier = Modifier.height(25.dp))
                Button(
                    onClick = onStart,
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 15.dp)
                        .height(50.dp),
                    shape = RoundedCornerShape(8.dp),
                    colors = ButtonDefaults.buttonColors(containerColor = Color.Transparent),
                    contentPadding = PaddingValues()
                ) {
                    Box(
                        modifier = Modifier
                            .fillMaxSize()
                            .background(
                                brush = Brush.horizontalGradient(
//                                    listOf(Color(0xFF61CFFF), Color(0xFF1B75F0))
                                    listOf(Color(0xFFF57128), Color(0xFFF57128))
                                ),
//                                shape = RoundedCornerShape(4.dp)
                            ),
                        contentAlignment = Alignment.Center
                    ) {
                        Text(
                            text = "LẬP TỨC TRẢI NGHIỆM",
                            color =  Color.White,
                            fontSize = 18.sp,
                            style = MaterialTheme.typography.headlineMedium
                        )
                    }
                }
            }
        }
    }
}
