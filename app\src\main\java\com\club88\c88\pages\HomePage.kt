package com.club88.c88.pages

import android.annotation.SuppressLint
import android.content.Context
import android.os.Build
import android.os.Bundle
import android.util.Log
import android.view.View
import android.view.ViewGroup
import android.webkit.ConsoleMessage
import android.webkit.GeolocationPermissions
import android.webkit.ValueCallback
import android.webkit.WebChromeClient
import android.webkit.WebSettings
import android.webkit.JavascriptInterface
import android.webkit.WebView
import android.webkit.WebViewClient
import androidx.activity.ComponentActivity
import androidx.activity.OnBackPressedCallback
import androidx.activity.compose.setContent
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.asPaddingValues
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.navigationBars
import androidx.compose.foundation.layout.statusBars
import androidx.compose.foundation.layout.windowInsetsBottomHeight
import androidx.compose.foundation.layout.windowInsetsPadding
import androidx.compose.foundation.layout.windowInsetsTopHeight
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
//import androidx.compose.ui.graphics.Color
import androidx.compose.ui.viewinterop.AndroidView
import androidx.core.view.WindowCompat
import androidx.core.view.WindowInsetsCompat
import androidx.core.view.WindowInsetsControllerCompat


import com.club88.c88.ui.theme.C88Theme
import com.club88.c88.BuildConfig

// JavaScript接口类，用于与网页通信
class WebViewJavaScriptInterface(private val webView: WebView) {
    @JavascriptInterface
    fun notifyViewportChange(width: Int, height: Int) {
        // 可以在这里添加日志或其他处理
        android.util.Log.d("WebView", "Viewport changed: ${width}x${height}")
    }
}

class HomePage : ComponentActivity() {
    private var webView: WebView? = null

    @SuppressLint("SetJavaScriptEnabled")
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        WindowCompat.setDecorFitsSystemWindows(window, false)

        // 创建 WindowInsetsControllerCompat 来控制系统栏样式
        val wic = WindowInsetsControllerCompat(window, window.decorView)
        wic.isAppearanceLightStatusBars = false
        window.statusBarColor = android.graphics.Color.TRANSPARENT

//        WindowCompat.getInsetsController(window, window.decorView).hide(WindowInsetsCompat.Type.statusBars())

        onBackPressedDispatcher.addCallback(this, object : OnBackPressedCallback(true) {
            override fun handleOnBackPressed() {
                webView?.let {
                    if (it.canGoBack()) {
                        it.goBack()
                    } else {
                        // 没得退了，先禁用回调然后调用默认行为退出 Activity
                        isEnabled = false
                        onBackPressedDispatcher.onBackPressed()
                    }
                } ?: run {
                    // webView 未创建，直接退出
                    isEnabled = false
                    onBackPressedDispatcher.onBackPressed()
                }
            }
        })

        setContent {
            C88Theme {


                Column(
                    modifier = Modifier
                        .fillMaxSize()
                        .background(Color.Black)
                ) {
                    // 顶部状态栏占位（高度 = statusBar）
                    StatusBarBackground()

                    // WebView 占满中间剩余空间
                    WebViewPage(
//                        url = "https://h5-dev.club88.asia",
                        url = "https://h5-dev.club88.asia?deviceType=app&codeVersion=3",
//                        url = "https://h5.club88live1.cc?deviceType=app&codeVersion=3",
//                        url= "https://h5-dev.pppicon.xyz?deviceType=app&codeVersion=3",
//                        url = "https://3s.uv128.com/7?acckey=046997696670dbd3c8d37ae519f18dfb&prefix=acp&langs=6&ot=1",
//                        url = "http://localhost:3003?deviceType=app&codeVersion=3",
                        onWebViewCreated = { webView = it },
                        modifier = Modifier.weight(1f)
                    )

                    // 底部导航栏占位（高度 = navigationBar）
                    NavigationBarBackground()
                }
            }
        }
    }

    override fun onDestroy() {
        webView?.destroy()
        super.onDestroy()
    }
}

@Composable
fun StatusBarBackground() {
    Box(
        Modifier
            .fillMaxWidth()
            .windowInsetsTopHeight(WindowInsets.statusBars)
            .background(Color(0xFF333333))     // 设置状态栏背景色
    )
}

@Composable
fun NavigationBarBackground() {
    Box(
        Modifier
            .fillMaxWidth()
            .windowInsetsBottomHeight(WindowInsets.navigationBars)
            .background(Color(0xFF333333))     // 设置状态栏背景色
    )
}

@SuppressLint("SetJavaScriptEnabled")
@Composable
fun WebViewPage(url: String, onWebViewCreated: (WebView) -> Unit, modifier: Modifier = Modifier) {
    AndroidView(
        modifier = modifier,
        factory = { context ->
            WebView(context).apply {
                // 先配置WebView，后加载URL

                // 添加JavaScript接口
                addJavascriptInterface(WebViewJavaScriptInterface(this), "AndroidInterface")

                // 配置WebViewClient
                webViewClient = object : WebViewClient() {
                    override fun onPageStarted(
                        view: WebView?,
                        url: String?,
                        favicon: android.graphics.Bitmap?
                    ) {
                        super.onPageStarted(view, url, favicon)
                        Log.i("WebView", "Page started loading: $url")
                    }

                    override fun onPageFinished(view: WebView?, url: String?) {
                        super.onPageFinished(view, url)
                        Log.i("WebView", "Page finished loading: $url")

                        // 页面加载完成后，通知网页当前视口尺寸
                        view?.post {
                            val width = view.width
                            val height = view.height
                            val script = """
                                (function() {
                                    // 通知网页当前可用的视口尺寸
                                    if (window.onViewportChanged) {
                                        window.onViewportChanged($width, $height);
                                    }

                                    // 设置CSS自定义属性，网页可以使用这些值
                                    document.documentElement.style.setProperty('--app-viewport-width', '${width}px');
                                    document.documentElement.style.setProperty('--app-viewport-height', '${height}px');

                                    // 触发自定义事件
                                    const event = new CustomEvent('appViewportChanged', {
                                        detail: { width: $width, height: $height }
                                    });
                                    window.dispatchEvent(event);
                                })();
                            """.trimIndent()

                            view.evaluateJavascript(script, null)
                        }
                    }

                    override fun onReceivedError(
                        view: WebView?,
                        request: android.webkit.WebResourceRequest?,
                        error: android.webkit.WebResourceError?
                    ) {
                        super.onReceivedError(view, request, error)
                        Log.e("WebView", "Error loading page: ${error?.description}")
                    }
                }

                // 配置WebChromeClient
                webChromeClient = object : WebChromeClient() {
                    // 支持地理位置权限
                    override fun onGeolocationPermissionsShowPrompt(
                        origin: String?,
                        callback: GeolocationPermissions.Callback?
                    ) {
                        callback?.invoke(origin, true, false)
                    }

                    // 支持文件上传
                    override fun onShowFileChooser(
                        webView: WebView?,
                        filePathCallback: ValueCallback<Array<android.net.Uri>>?,
                        fileChooserParams: FileChooserParams?
                    ): Boolean {
                        // 这里可以实现文件选择逻辑
                        // 暂时返回 false 使用默认行为
                        return false
                    }

                    // 控制台消息处理
                    override fun onConsoleMessage(consoleMessage: ConsoleMessage?): Boolean {
                        consoleMessage?.let {
                            Log.d("WebView Console", "${it.message()} -- From line ${it.lineNumber()} of ${it.sourceId()}")
                        }
                        return true
                    }

                    override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
                        super.onSizeChanged(w, h, oldw, oldh)
                        Log.d("WebView", "Size changed: ${w}x${h}, old: ${oldw}x${oldh}")
                    }
                }

                // 配置CookieManager - 确保在加载URL前配置
                val cookieManager = android.webkit.CookieManager.getInstance()
                cookieManager.setAcceptCookie(true) // 启用基础cookie支持

                // 明确启用第三方cookie支持
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                    cookieManager.setAcceptThirdPartyCookies(this, true)
                }

                // 确保cookie同步
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                    cookieManager.flush()
                } else {
                    android.webkit.CookieSyncManager.getInstance().sync()
                }

                // WebView设置
                settings.apply {
                    // 基础设置
                    javaScriptEnabled = true
                    domStorageEnabled = true
                    databaseEnabled = true

                    // 启用cookie
                    javaScriptCanOpenWindowsAutomatically = true

                    // 用户代理字符串
                    userAgentString = "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36"

                    // 视口和缩放设置
                    loadWithOverviewMode = true
                    useWideViewPort = true
                    setSupportZoom(true)
                    builtInZoomControls = true
                    displayZoomControls = false

                    // 文件访问权限
                    allowFileAccess = true
                    allowContentAccess = true

                    // 缓存设置
                    cacheMode = WebSettings.LOAD_DEFAULT

                    // 媒体播放设置
                    mediaPlaybackRequiresUserGesture = false

                    // 地理位置设置
                    setGeolocationEnabled(true)

                    // 安全和混合内容设置
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                        mixedContentMode = WebSettings.MIXED_CONTENT_ALWAYS_ALLOW
                    }

                    // 文本缩放设置
                    textZoom = 100

                    // 渲染优先级
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
                        setLayoutAlgorithm(WebSettings.LayoutAlgorithm.TEXT_AUTOSIZING)
                    }

                    // 支持多窗口
                    setSupportMultipleWindows(false)

                    // 加载图片
                    loadsImagesAutomatically = true

                    // 允许从文件URL访问文件URL（谨慎使用）
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN) {
                        allowFileAccessFromFileURLs = false
                        allowUniversalAccessFromFileURLs = false
                    }

                    // 安全浏览
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                        safeBrowsingEnabled = true
                    }
                }

                // 确保硬件加速开启
                setLayerType(View.LAYER_TYPE_HARDWARE, null)

                // 开启调试
                if (BuildConfig.DEBUG) {
                    WebView.setWebContentsDebuggingEnabled(true)
                }

                // 设置布局参数
                layoutParams = ViewGroup.LayoutParams(
                    ViewGroup.LayoutParams.MATCH_PARENT,
                    ViewGroup.LayoutParams.MATCH_PARENT
                )

                // 监听WebView尺寸变化
                viewTreeObserver.addOnGlobalLayoutListener {
                    val currentWidth = width
                    val currentHeight = height

                    if (currentWidth > 0 && currentHeight > 0) {
                        // 当尺寸变化时，通知网页
                        val script = """
                            (function() {
                                // 通知网页视口尺寸变化
                                if (window.onViewportChanged) {
                                    window.onViewportChanged($currentWidth, $currentHeight);
                                }

                                // 更新CSS自定义属性
                                document.documentElement.style.setProperty('--app-viewport-width', '${currentWidth}px');
                                document.documentElement.style.setProperty('--app-viewport-height', '${currentHeight}px');

                                // 触发自定义事件
                                const event = new CustomEvent('appViewportChanged', {
                                    detail: { width: $currentWidth, height: $currentHeight }
                                });
                                window.dispatchEvent(event);
                            })();
                        """.trimIndent()

                        evaluateJavascript(script, null)
                        Log.d("WebView", "Size changed to: ${currentWidth}x${currentHeight}")
                    }
                }

                // 通知回调
                onWebViewCreated(this)

                // 所有配置完成后，最后加载URL
                loadUrl(url)
            }
        }
    )
}


