package com.club88.c88

import android.content.Intent
import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.compose.runtime.*
import androidx.datastore.preferences.core.booleanPreferencesKey
import androidx.datastore.preferences.core.edit
import androidx.datastore.preferences.preferencesDataStore
import androidx.lifecycle.lifecycleScope
import com.club88.c88.ui.theme.C88Theme
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.launch

import com.club88.c88.pages.AppFuncPage
import com.club88.c88.pages.CountDownPage
import com.club88.c88.pages.HomePage


private val ComponentActivity.dataStore by preferencesDataStore(name = "app_prefs")

class MainActivity : ComponentActivity() {

    private val FIRST_LAUNCH_KEY = booleanPreferencesKey("first_launch")

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()

        var isFirstLaunch by mutableStateOf<Boolean?>(null)

        // 异步读取是否第一次启动
        lifecycleScope.launch {
            val prefs = dataStore.data.first()
            val firstLaunch = prefs[FIRST_LAUNCH_KEY] ?: true
            isFirstLaunch = firstLaunch

            // 如果是第一次，就把 firstLaunch 设为 false
            if (firstLaunch) {
                dataStore.edit { it[FIRST_LAUNCH_KEY] = false }
            }
        }

        setContent {
            C88Theme {
                when (isFirstLaunch) {
                    null -> {} // 加载中，可加个 loading UI
//                    true -> CountDownPage()
//                    false -> AppFuncPage()
                    true -> AppFuncPage(onCompleted = {
                        startActivity(Intent(this@MainActivity, HomePage::class.java))
                        finish()
                    })

                    false -> CountDownPage(onCompleted = {
                        startActivity(Intent(this@MainActivity, HomePage::class.java))
                        finish()
                    })
                }
            }
        }
    }
}
