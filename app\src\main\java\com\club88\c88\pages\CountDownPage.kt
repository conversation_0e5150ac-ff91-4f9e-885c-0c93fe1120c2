package com.club88.c88.pages

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.LinearProgressIndicator
import androidx.compose.material3.Text
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import kotlinx.coroutines.delay
import com.club88.c88.R

@Composable
fun CountDownPage(
    onCompleted: () -> Unit
) {
    var countdown by remember { mutableIntStateOf(3) }
    val progress by remember { derivedStateOf { (3 - countdown).toFloat() / 3 } }

    LaunchedEffect(Unit) {
        while (countdown > 0) {
            delay(1000L)
            countdown--
        }
        // TODO: navigate to home
        onCompleted()
    }

    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(Color.Black) // 临时背景色，覆盖掉后面bg图
    ) {
        Image(
            painter = painterResource(id = R.drawable.bg),
            contentDescription = null,
            contentScale = ContentScale.Crop,
            modifier = Modifier.matchParentSize()
        )

        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(horizontal = 24.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center
        ) {
            Image(
                painter = painterResource(id = R.drawable.ic_first_logo),
                contentDescription = null,
                modifier = Modifier
                    .width(140.dp)
            )

            Spacer(modifier = Modifier.height(90.dp))

            Image(
                painter = painterResource(id = R.drawable.countdown_main),
                contentDescription = null,
                modifier = Modifier
                    .fillMaxWidth()
                    .aspectRatio(1408f / 1164f)
            )

            Spacer(modifier = Modifier.height(80.dp))

//            LinearProgressIndicator(
//                progress = {progress},
//                color = Color(0xffff881e ),
//                trackColor = Color(0xFF4b333a),
//                modifier = Modifier
//                    .fillMaxWidth()
//                    .height(10.dp)
//                    .clip(RoundedCornerShape(6.dp))
//            )
            CustomLinearProgressIndicator(
                progress =  progress,
                modifier = Modifier.fillMaxWidth()
            )


            Spacer(modifier = Modifier.height(10.dp))

            Text(
                text = "Đang Tải, Vui Lòng Chờ ···",
                fontSize = 16.sp,
                color = Color(0xFF4D595E)
            )
        }
    }
}

@Composable
fun CustomLinearProgressIndicator(
    progress: Float,
    modifier: Modifier = Modifier,
    backgroundColor: Color = Color(0xFF4b333a),
    progressColor: Color = Color(0xffff881e),
    height: Dp = 10.dp,
    cornerRadius: Dp = 6.dp,
) {
    Box(
        modifier = modifier
            .height(height)
            .clip(RoundedCornerShape(cornerRadius))
            .background(backgroundColor)
    ) {
        Box(
            modifier = Modifier
                .fillMaxHeight()
                .fillMaxWidth(progress.coerceIn(0f, 1f))
                .background(progressColor)
        )
    }
}

